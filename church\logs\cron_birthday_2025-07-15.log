[2025-07-15 00:26:15] Request received: format=json, cron_key=fac***
[2025-07-15 00:26:15] Request details: {"DOCUMENT_ROOT":"C:\\xampp\\htdocs\\campaign\\church","REMOTE_ADDR":"::1","REMOTE_PORT":"65070","SERVER_SOFTWARE":"PHP 8.2.12 Development Server","SERVER_PROTOCOL":"HTTP\/1.1","SERVER_NAME":"localhost","SERVER_PORT":"8000","REQUEST_URI":"\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_METHOD":"GET","SCRIPT_NAME":"\/birthday_reminders.php","SCRIPT_FILENAME":"C:\\xampp\\htdocs\\campaign\\church\\birthday_reminders.php","PHP_SELF":"\/birthday_reminders.php","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","HTTP_HOST":"localhost:8000","HTTP_CONNECTION":"keep-alive","HTTP_SEC_CH_UA":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"","HTTP_SEC_CH_UA_MOBILE":"?0","HTTP_SEC_CH_UA_PLATFORM":"\"Windows\"","HTTP_UPGRADE_INSECURE_REQUESTS":"1","HTTP_USER_AGENT":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36 Edg\/*********","HTTP_ACCEPT":"text\/html,application\/xhtml+xml,application\/xml;q=0.9,image\/avif,image\/webp,image\/apng,*\/*;q=0.8,application\/signed-exchange;v=b3;q=0.7","HTTP_SEC_FETCH_SITE":"none","HTTP_SEC_FETCH_MODE":"navigate","HTTP_SEC_FETCH_USER":"?1","HTTP_SEC_FETCH_DEST":"document","HTTP_ACCEPT_ENCODING":"gzip, deflate, br, zstd","HTTP_ACCEPT_LANGUAGE":"en-US,en;q=0.9","HTTP_COOKIE":"language=en; PHPSESSID=b07ud4a96kfppph68rrupu4jrj","REQUEST_TIME_FLOAT":1752531975.13009,"REQUEST_TIME":1752531975}
[2025-07-15 00:26:15] Access granted: via cron key
[2025-07-15 00:26:15] Starting birthday reminders cron job
[2025-07-15 00:26:15] Database connection successful. Found 4 total members.
[2025-07-15 00:29:54] Request received: format=json, cron_key=fac***
[2025-07-15 00:29:54] Request details: {"DOCUMENT_ROOT":"C:\\xampp\\htdocs\\campaign\\church","REMOTE_ADDR":"::1","REMOTE_PORT":"65447","SERVER_SOFTWARE":"PHP 8.2.12 Development Server","SERVER_PROTOCOL":"HTTP\/1.1","SERVER_NAME":"localhost","SERVER_PORT":"8000","REQUEST_URI":"\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_METHOD":"GET","SCRIPT_NAME":"\/birthday_reminders.php","SCRIPT_FILENAME":"C:\\xampp\\htdocs\\campaign\\church\\birthday_reminders.php","PHP_SELF":"\/birthday_reminders.php","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","HTTP_HOST":"localhost:8000","HTTP_ACCEPT":"*\/*","REQUEST_TIME_FLOAT":1752532194.849441,"REQUEST_TIME":1752532194}
[2025-07-15 00:29:54] Access granted: via cron key
[2025-07-15 00:29:54] Starting birthday reminders cron job
[2025-07-15 00:29:54] Database connection successful. Found 4 total members.
[2025-07-15 00:30:32] Request received: format=json, cron_key=fac***
[2025-07-15 00:30:32] Request details: {"DOCUMENT_ROOT":"C:\\xampp\\htdocs\\campaign\\church","REMOTE_ADDR":"::1","REMOTE_PORT":"65502","SERVER_SOFTWARE":"PHP 8.2.12 Development Server","SERVER_PROTOCOL":"HTTP\/1.1","SERVER_NAME":"localhost","SERVER_PORT":"8000","REQUEST_URI":"\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_METHOD":"GET","SCRIPT_NAME":"\/birthday_reminders.php","SCRIPT_FILENAME":"C:\\xampp\\htdocs\\campaign\\church\\birthday_reminders.php","PHP_SELF":"\/birthday_reminders.php","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","HTTP_HOST":"localhost:8000","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","REQUEST_TIME_FLOAT":1752532232.500415,"REQUEST_TIME":1752532232}
[2025-07-15 00:30:32] Access granted: via cron key
[2025-07-15 00:30:32] Starting birthday reminders cron job
[2025-07-15 00:30:32] Database connection successful. Found 4 total members.
[2025-07-15 00:30:39] Request received: format=json, cron_key=fac***
[2025-07-15 00:30:39] Request details: {"DOCUMENT_ROOT":"C:\\xampp\\htdocs\\campaign\\church","REMOTE_ADDR":"::1","REMOTE_PORT":"65519","SERVER_SOFTWARE":"PHP 8.2.12 Development Server","SERVER_PROTOCOL":"HTTP\/1.1","SERVER_NAME":"localhost","SERVER_PORT":"8000","REQUEST_URI":"\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_METHOD":"GET","SCRIPT_NAME":"\/birthday_reminders.php","SCRIPT_FILENAME":"C:\\xampp\\htdocs\\campaign\\church\\birthday_reminders.php","PHP_SELF":"\/birthday_reminders.php","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","HTTP_HOST":"localhost:8000","HTTP_ACCEPT":"*\/*","REQUEST_TIME_FLOAT":1752532239.106759,"REQUEST_TIME":1752532239}
[2025-07-15 00:30:39] Access granted: via cron key
[2025-07-15 00:30:39] Starting birthday reminders cron job
[2025-07-15 00:30:39] Database connection successful. Found 4 total members.
[2025-07-15 00:32:53] Request received: format=json, cron_key=fac***
[2025-07-15 00:32:53] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"49716","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1752532373.224076,"REQUEST_TIME":1752532373}
[2025-07-15 00:32:53] Access granted: via cron key
[2025-07-15 00:32:53] Starting birthday reminders cron job
[2025-07-15 00:32:53] Database connection successful. Found 4 total members.
[2025-07-15 00:33:05] Request received: format=json, cron_key=fac***
[2025-07-15 00:33:05] Request details: {"MIBDIRS":"C:\/xampp\/php\/extras\/mibs","MYSQL_HOME":"\\xampp\\mysql\\bin","OPENSSL_CONF":"C:\/xampp\/apache\/bin\/openssl.cnf","PHP_PEAR_SYSCONF_DIR":"\\xampp\\php","PHPRC":"\\xampp\\php","TMP":"\\xampp\\tmp","HTTP_HOST":"localhost","HTTP_USER_AGENT":"Church Admin Panel Cron Check","HTTP_ACCEPT":"*\/*","HTTP_X_REQUESTED_WITH":"XMLHttpRequest","HTTP_CACHE_CONTROL":"no-cache","HTTP_X_CRON_CHECK":"true","PATH":"C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Redis;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\xampp\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code Insiders\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin","SystemRoot":"C:\\WINDOWS","COMSPEC":"C:\\WINDOWS\\system32\\cmd.exe","PATHEXT":".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC","WINDIR":"C:\\WINDOWS","SERVER_SIGNATURE":"<address>Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12 Server at localhost Port 80<\/address>\n","SERVER_SOFTWARE":"Apache\/2.4.58 (Win64) OpenSSL\/3.1.3 PHP\/8.2.12","SERVER_NAME":"localhost","SERVER_ADDR":"::1","SERVER_PORT":"80","REMOTE_ADDR":"::1","DOCUMENT_ROOT":"C:\/xampp\/htdocs","REQUEST_SCHEME":"http","CONTEXT_PREFIX":"","CONTEXT_DOCUMENT_ROOT":"C:\/xampp\/htdocs","SERVER_ADMIN":"postmaster@localhost","SCRIPT_FILENAME":"C:\/xampp\/htdocs\/campaign\/church\/birthday_reminders.php","REMOTE_PORT":"50536","GATEWAY_INTERFACE":"CGI\/1.1","SERVER_PROTOCOL":"HTTP\/1.1","REQUEST_METHOD":"GET","QUERY_STRING":"format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","REQUEST_URI":"\/campaign\/church\/birthday_reminders.php?format=json&check=1&cron_key=fac_2024_secure_cron_8x9q2p5m","SCRIPT_NAME":"\/campaign\/church\/birthday_reminders.php","PHP_SELF":"\/campaign\/church\/birthday_reminders.php","REQUEST_TIME_FLOAT":1752532385.166003,"REQUEST_TIME":1752532385}
[2025-07-15 00:33:05] Access granted: via cron key
[2025-07-15 00:33:05] Starting birthday reminders cron job
[2025-07-15 00:33:05] Database connection successful. Found 4 total members.
