<?php
// Test script to verify age calculation consistency
require_once 'config.php';
require_once 'send_birthday_reminders.php';

echo "=== Age Calculation Consistency Test ===\n";

// Get Ndivhuwo's data
$stmt = $pdo->prepare('SELECT * FROM members WHERE full_name LIKE "%Ndivhuwo%" LIMIT 1');
$stmt->execute();
$member = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$member) {
    echo "Member not found\n";
    exit;
}

echo "Testing for: {$member['full_name']}\n";
echo "Birth Date: {$member['birth_date']}\n";
echo "Today: " . date('Y-m-d') . "\n\n";

// Test 1: Direct DateTime calculation
$birth = new DateTime($member['birth_date']);
$today = new DateTime();
$age1 = $today->diff($birth)->y;
echo "1. Direct DateTime method: $age1 years\n";

// Test 2: Simulate BirthdayReminder age calculation
try {
    $birth = new DateTime($member['birth_date']);
    $today = new DateTime();
    $age2 = $today->diff($birth)->y;
    echo "2. BirthdayReminder simulation: $age2 years\n";
} catch (Exception $e) {
    echo "2. BirthdayReminder simulation: Error - " . $e->getMessage() . "\n";
}

// Test 3: Another DateTime calculation (like in fixed code)
try {
    $birth = new DateTime($member['birth_date']);
    $today = new DateTime();
    $age3 = $today->diff($birth)->y;
    echo "3. Fixed code simulation: $age3 years\n";
} catch (Exception $e) {
    echo "3. Fixed code simulation: Error - " . $e->getMessage() . "\n";
}

// Test 4: Simulate notification email processing
$memberData = [
    'full_name' => $member['full_name'],
    'birth_date' => $member['birth_date'],
    'email' => $member['email']
];

// Simulate the notification processing
$birth = new DateTime($member['birth_date']);
$today = new DateTime();
$age4 = $today->diff($birth)->y;
$memberData['birthday_member_age'] = $age4;

echo "4. Notification processing simulation: {$memberData['birthday_member_age']} years\n";

echo "\n=== Results ===\n";
if ($age1 == $age2 && $age2 == $age3 && $age3 == $age4) {
    echo "✅ SUCCESS: All age calculations are consistent ($age1 years)\n";
    echo "The age calculation fix is working correctly!\n";
} else {
    echo "❌ ERROR: Age calculations are inconsistent:\n";
    echo "   Direct: $age1, Simulation: $age2, ReminderSystem: $age3, Notification: $age4\n";
    echo "There may still be some inconsistent calculation methods.\n";
}

echo "\nExpected age: 38 years (since birthday is tomorrow, July 16, 2025)\n";
?>
