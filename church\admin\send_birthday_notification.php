<?php
// Set error reporting and execution time
ini_set('display_errors', 1);
error_reporting(E_ALL);
set_time_limit(300); // 5 minutes max execution time

// Start session
session_start();

// Include configuration
require_once '../config.php';
require_once '../send_birthday_reminders.php';

// Initialize variables
$success_message = '';
$error_message = '';

// Get admin email from settings, config, or set a default
$stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'admin_email' LIMIT 1");
$stmt->execute();
$setting_admin_email = $stmt->fetchColumn();

$admin_email = $setting_admin_email ?: (defined('ADMIN_EMAIL') ? ADMIN_EMAIL : '<EMAIL>');

// Initialize BirthdayReminder with admin email
$birthdayReminder = new BirthdayReminder($pdo, $admin_email);
$templates = [];
$upcomingBirthdays = [];

// Function to get upcoming birthdays
function getUpcomingBirthdays($pdo, $daysToCheck = 14) {
    $results = [];
    $today = date('Y-m-d');
    
    for ($day = 0; $day < $daysToCheck; $day++) {
        $checkDate = date('Y-m-d', strtotime("+$day days"));
        $month = date('m', strtotime($checkDate));
        $day_of_month = date('d', strtotime($checkDate));
        
        // Find members with birthdays on this date
        $stmt = $pdo->prepare("
            SELECT *, 
                   DATE_FORMAT(birth_date, '%m-%d') as birth_day, 
                   DATE_FORMAT(?, '%m-%d') as check_day,
                   ? as days_until_birthday
            FROM members 
            WHERE DATE_FORMAT(birth_date, '%m-%d') = DATE_FORMAT(?, '%m-%d')
            ORDER BY full_name
        ");
        $stmt->execute([$checkDate, $day, $checkDate]);
        $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($members)) {
            $formattedDate = date('l, F j, Y', strtotime($checkDate));
            $results[] = [
                'date' => $checkDate,
                'formatted_date' => $formattedDate,
                'days_until' => $day,
                'members' => $members,
                'count' => count($members)
            ];
        }
    }
    
    return $results;
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Check if this is an AJAX request to get member details
    if (isset($_POST['get_birthday_member']) && isset($_POST['member_id'])) {
        try {
            $memberId = (int)$_POST['member_id'];
            $days_until = isset($_POST['days_until']) ? (int)$_POST['days_until'] : 0;
            
            // Get member details
            $stmt = $pdo->prepare("SELECT * FROM members WHERE id = ?");
            $stmt->execute([$memberId]);
            $member = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Format for display
            if ($member) {
                // Clean up the image path for display in the modal
                $imagePath = $member['image_path'] ?? '';
                // Remove any domain prefix if it exists
                $imagePath = preg_replace('/^https?:\/\/[^\/]+\//', '', $imagePath);
                
                // Check if image exists, if not use default
                if (empty($imagePath) || !file_exists("../$imagePath")) {
                    $imagePath = 'assets/img/default-avatar.png';
                }
                
                $response = [
                    'success' => true,
                    'member' => [
                        'id' => $member['id'],
                        'full_name' => $member['full_name'],
                        'email' => $member['email'],
                        'phone_number' => $member['phone_number'] ?? 'Not provided',
                        'birth_date' => date('F j, Y', strtotime($member['birth_date'])),
                        'days_until' => $days_until,
                        'image_path' => $imagePath
                    ]
                ];
            } else {
                $response = [
                    'success' => false,
                    'error' => 'Member not found'
                ];
            }
            
            // Return JSON response
            header('Content-Type: application/json');
            echo json_encode($response);
            exit;
        } catch (Exception $e) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'error' => 'Error retrieving member details: ' . $e->getMessage()
            ]);
            exit;
        }
    }
    
    // Check if this is a request to send notifications
    if (isset($_POST['send_notifications']) && isset($_POST['birthday_member_id'])) {
        $birthdayMemberId = $_POST['birthday_member_id'];
        $templateId = isset($_POST['template_id']) ? $_POST['template_id'] : null;
        $daysUntil = isset($_POST['days_until']) ? (int)$_POST['days_until'] : 0;
        
        try {
            // Set email type for proper logging
            $birthdayReminder->setEmailType('b_notification');

            // Send notifications
            $result = $birthdayReminder->sendMemberBirthdayNotifications(
                $birthdayMemberId,
                $templateId,
                $daysUntil
            );

            if (isset($result['error'])) {
                $error_message = $result['error'];
            } else {
                $success_message = "Successfully sent {$result['success']} notification emails about {$result['birthday_member']}'s birthday!";
                if ($result['failed'] > 0) {
                    $success_message .= " ({$result['failed']} failed)";
                }
            }
        } catch (Exception $e) {
            $error_message = "Error sending notifications: " . $e->getMessage();
        }
        
        // If this is an AJAX request, return JSON
        if (isset($_POST['ajax']) && $_POST['ajax'] == 1) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => !empty($success_message),
                'message' => !empty($success_message) ? $success_message : $error_message
            ]);
            exit;
        }
    }
}

// Get notification templates
$stmt = $pdo->prepare("SELECT * FROM email_templates WHERE template_name LIKE '%Birthday Notification%' OR template_name LIKE '%Member Notification%' ORDER BY template_name");
$stmt->execute();
$templates = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get upcoming birthdays
$upcomingBirthdays = getUpcomingBirthdays($pdo, 14);

// Get total member count for notification context
$stmt = $pdo->prepare("SELECT COUNT(*) as total FROM members WHERE email IS NOT NULL AND email != ''");
$stmt->execute();
$totalMembers = $stmt->fetch(PDO::FETCH_ASSOC)['total'] ?? 0;

// Page title and variables for header
$page_title = "Send Birthday Notifications";
$page_header = "Send Birthday Notifications";
$page_description = "Send notifications to church members about upcoming birthdays";

// Include header
include 'includes/header.php';
?>

<!-- Main content container -->
<div class="container-fluid">
    <!-- Add notification container -->
    <div id="notificationContainer"></div>

    <style>
        .member-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #3498db;
            margin-bottom: 1rem;
        }
        
        .modal-body .text-center {
            margin-bottom: 1.5rem;
        }
        
        #memberName {
            font-size: 1.5rem;
            margin-top: 0.5rem;
        }

        .notification-alert {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            max-width: 500px;
            animation: slideIn 0.5s ease-in-out;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .notification-alert.fade-out {
            animation: fadeOut 0.5s ease-in-out forwards;
        }

        @keyframes fadeOut {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }
    </style>

    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle-fill me-2"></i><?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i><?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="bi bi-info-circle me-1"></i>
            About Birthday Notifications
        </div>
        <div class="card-body">
            <p>This feature allows you to send notifications to all church members about another member's upcoming birthday. 
            This helps foster community and encourages everyone to celebrate together.</p>
            
            <p>When you send a notification, <?php echo ($totalMembers - 1); ?> members will receive an email about the birthday person.</p>
            
            <div class="alert alert-info">
                <strong>Tip:</strong> These notifications are different from direct birthday emails. These are sent to everyone <em>except</em> the person having the birthday.
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <i class="bi bi-gift me-1"></i>
                    Upcoming Birthdays
                </div>
                <div class="card-body">
                    <?php if (empty($upcomingBirthdays)): ?>
                        <div class="alert alert-warning">
                            No upcoming birthdays in the next 14 days.
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Name</th>
                                        <th>Days Until</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($upcomingBirthdays as $date): ?>
                                        <?php foreach ($date['members'] as $member): ?>
                                            <tr>
                                                <td><?php echo $date['formatted_date']; ?></td>
                                                <td><?php echo htmlspecialchars($member['full_name']); ?></td>
                                                <td>
                                                    <?php if ($date['days_until'] == 0): ?>
                                                        <span class="badge bg-danger">Today!</span>
                                                    <?php elseif ($date['days_until'] == 1): ?>
                                                        <span class="badge bg-warning">Tomorrow</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-info">In <?php echo $date['days_until']; ?> days</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <button class="btn btn-sm btn-primary view-member" 
                                                            data-id="<?php echo $member['id']; ?>" 
                                                            data-days="<?php echo $date['days_until']; ?>">
                                                        <i class="bi bi-eye"></i> View
                                                    </button>
                                                    <button class="btn btn-sm btn-success send-notification" 
                                                            data-id="<?php echo $member['id']; ?>" 
                                                            data-days="<?php echo $date['days_until']; ?>"
                                                            data-name="<?php echo htmlspecialchars($member['full_name']); ?>">
                                                        <i class="bi bi-send"></i> Send
                                                    </button>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <i class="bi bi-envelope me-1"></i>
                    Birthday Notification Templates
                </div>
                <div class="card-body">
                    <?php if (empty($templates)): ?>
                        <div class="alert alert-warning">
                            No birthday notification templates found. Please create templates with names containing "Birthday Notification" or "Member Notification".
                        </div>
                        <a href="email_templates.php" class="btn btn-primary">
                            <i class="bi bi-plus-circle"></i> Create Template
                        </a>
                    <?php else: ?>
                        <div class="list-group">
                            <?php foreach ($templates as $template): ?>
                            <div class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h5 class="mb-1"><?php echo htmlspecialchars($template['template_name']); ?></h5>
                                </div>
                                <p class="mb-1"><?php echo htmlspecialchars($template['subject']); ?></p>
                                <div class="d-flex justify-content-end mt-2">
                                    <a href="preview_template.php?id=<?php echo $template['id']; ?>" class="btn btn-sm btn-info me-2">
                                        <i class="bi bi-eye"></i> Preview
                                    </a>
                                    <a href="email_templates.php?edit_id=<?php echo $template['id']; ?>" class="btn btn-sm btn-primary">
                                        <i class="bi bi-pencil"></i> Edit
                                    </a>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Member Details Modal -->
    <div class="modal fade" id="memberDetailsModal" tabindex="-1" role="dialog" aria-labelledby="memberDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="memberDetailsModalLabel">Member Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-3">
                        <img id="memberImage" class="member-avatar img-fluid rounded-circle mb-2" src="" alt="Member image">
                        <h4 id="memberName" class="mb-0"></h4>
                    </div>
                    <div class="mt-3">
                        <table class="table table-bordered">
                            <tr>
                                <th>Email:</th>
                                <td id="memberEmail"></td>
                            </tr>
                            <tr>
                                <th>Phone:</th>
                                <td id="memberPhone"></td>
                            </tr>
                            <tr>
                                <th>Days Until Birthday:</th>
                                <td id="memberDaysUntil"></td>
                            </tr>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-success" id="sendNotificationBtn">Send Notification</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Send Notification Modal -->
    <div class="modal fade" id="notificationModal" tabindex="-1" role="dialog" aria-labelledby="notificationModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="notificationModalLabel">Send Birthday Notification</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="notificationForm" method="post">
                    <div class="modal-body">
                        <input type="hidden" name="send_notifications" value="1">
                        <input type="hidden" name="ajax" value="1">
                        <input type="hidden" id="birthdayMemberId" name="birthday_member_id" value="">
                        <input type="hidden" id="daysUntil" name="days_until" value="">
                        
                        <div class="alert alert-info">
                            You are about to send a notification to all church members about <strong id="notificationMemberName"></strong>'s birthday using the selected template style.
                        </div>
                        
                        <div class="mb-3">
                            <label for="templateSelect" class="form-label">Select Template:</label>
                            <select class="form-select" id="templateSelect" name="template_id" required>
                                <?php foreach ($templates as $template): ?>
                                <option value="<?php echo $template['id']; ?>"><?php echo htmlspecialchars($template['template_name']); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Template Preview:</label>
                            <div id="templatePreview" class="border p-3 rounded bg-light">
                                <iframe id="preview-frame" style="width: 100%; height: 300px; border: none;"></iframe>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-success">Send Notification</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
<script>
$(document).ready(function() {
    // Function to show notification
    function showNotification(message, type = 'success') {
        const icon = type === 'success' ? 'bi-check-circle-fill' : 'bi-exclamation-triangle-fill';
        const alert = $(`
            <div class="notification-alert alert alert-${type} alert-dismissible fade show" role="alert">
                <i class="bi ${icon} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `);

        // Add to container
        $('#notificationContainer').append(alert);

        // Auto dismiss after 5 seconds
        setTimeout(() => {
            alert.addClass('fade-out');
            setTimeout(() => {
                alert.remove();
            }, 500);
        }, 5000);
    }

    // View member details
    $('.view-member').click(function() {
        const memberId = $(this).data('id');
        const days = $(this).data('days');
        
        // Get member details
        $.ajax({
            url: 'send_birthday_notification.php',
            method: 'POST',
            data: {
                get_birthday_member: 1,
                member_id: memberId,
                days_until: days
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    const member = response.member;
                    $('#memberName').text(member.full_name);
                    $('#memberEmail').text(member.email);
                    $('#memberPhone').text(member.phone_number);
                    $('#memberDaysUntil').text(member.days_until == 0 ? 'Today!' : (member.days_until == 1 ? 'Tomorrow' : `In ${member.days_until} days`));
                    
                    if (member.image_path) {
                        const imagePath = member.image_path.replace(/^\//, '');
                        $('#memberImage').attr('src', '../' + imagePath).show();
                    } else {
                        const initials = member.full_name.split(' ').map(n => n[0]).join('');
                        $('#memberImage').attr('src', `https://via.placeholder.com/150/3498db/ffffff?text=${initials}`).show();
                    }
                    
                    $('#sendNotificationBtn').data('id', member.id).data('days', member.days_until).data('name', member.full_name);
                    
                    $('#memberDetailsModal').modal('show');
                } else {
                    showNotification(response.error, 'danger');
                }
            },
            error: function() {
                showNotification('Error fetching member details', 'danger');
            }
        });
    });
    
    // Send notification button in member details modal
    $('#sendNotificationBtn').click(function() {
        const memberId = $(this).data('id');
        const days = $(this).data('days');
        const name = $(this).data('name');
        
        $('#birthdayMemberId').val(memberId);
        $('#daysUntil').val(days);
        $('#notificationMemberName').text(name);
        
        // Close member details modal
        $('#memberDetailsModal').modal('hide');
        
        // Show notification modal
        $('#notificationModal').modal('show');
        
        // Load template preview
        loadTemplatePreview();
    });
    
    // Send notification directly
    $('.send-notification').click(function() {
        const memberId = $(this).data('id');
        const days = $(this).data('days');
        const name = $(this).data('name');
        
        $('#birthdayMemberId').val(memberId);
        $('#daysUntil').val(days);
        $('#notificationMemberName').text(name);
        
        // Show notification modal
        $('#notificationModal').modal('show');
        
        // Load template preview
        loadTemplatePreview();
    });
    
    // Update preview when template changes
    $('#templateSelect').change(function() {
        loadTemplatePreview();
    });
    
    // Function to load template preview
    function loadTemplatePreview() {
        const templateId = $('#templateSelect').val();
        const previewUrl = `preview_template.php?id=${templateId}&embed=1`;
        $('#preview-frame').attr('src', previewUrl);
    }
    
    // Handle notification form submission
    $('#notificationForm').submit(function(e) {
        e.preventDefault();
        
        $('.modal-footer button').prop('disabled', true);
        $('.modal-footer button[type="submit"]').html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Sending...');
        
        $.ajax({
            url: 'send_birthday_notification.php',
            method: 'POST',
            data: $(this).serialize(),
            dataType: 'json',
            success: function(response) {
                $('#notificationModal').modal('hide');
                
                if (response.success) {
                    showNotification(response.message, 'success');
                    setTimeout(() => {
                        location.reload();
                    }, 2000);
                } else {
                    showNotification(response.message, 'danger');
                    $('.modal-footer button').prop('disabled', false);
                    $('.modal-footer button[type="submit"]').html('Send Notification');
                }
            },
            error: function() {
                showNotification('Error sending notifications', 'danger');
                $('.modal-footer button').prop('disabled', false);
                $('.modal-footer button[type="submit"]').html('Send Notification');
            }
        });
    });
});
</script>

<?php
// Include footer
include 'includes/footer.php';
?> 