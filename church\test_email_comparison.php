<?php
// Test script to compare email sending between admin/send_birthday_message.php and admin/send_birthday_emails.php
session_start();
$_SESSION['admin_id'] = 1; // Set admin session for testing

require_once 'config.php';

echo "<h1>Email Sending Comparison Test</h1>\n";

// Get a member with a profile picture
$stmt = $pdo->prepare('SELECT * FROM members WHERE image_path IS NOT NULL AND image_path != "" LIMIT 1');
$stmt->execute();
$testMember = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$testMember) {
    echo "<p style='color: red;'>No members with profile pictures found. Please add a member with a profile picture first.</p>\n";
    exit;
}

echo "<h2>Test Member:</h2>\n";
echo "<p><strong>Name:</strong> " . htmlspecialchars($testMember['full_name']) . "</p>\n";
echo "<p><strong>Email:</strong> " . htmlspecialchars($testMember['email']) . "</p>\n";
echo "<p><strong>Image Path:</strong> " . htmlspecialchars($testMember['image_path']) . "</p>\n";

// Get a birthday template
$stmt = $pdo->prepare('SELECT * FROM email_templates WHERE is_birthday_template = 1 LIMIT 1');
$stmt->execute();
$template = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$template) {
    echo "<p style='color: red;'>No birthday templates found. Please create a birthday template first.</p>\n";
    exit;
}

echo "<h2>Test Template:</h2>\n";
echo "<p><strong>Name:</strong> " . htmlspecialchars($template['template_name']) . "</p>\n";
echo "<p><strong>Subject:</strong> " . htmlspecialchars($template['subject']) . "</p>\n";

// Test 1: Using replaceTemplatePlaceholders (like admin/send_birthday_message.php)
echo "<h2>Test 1: Using replaceTemplatePlaceholders function (admin/send_birthday_message.php method)</h2>\n";

$processedSubject1 = replaceTemplatePlaceholders($template['subject'], $testMember);
$processedContent1 = replaceTemplatePlaceholders($template['content'], $testMember);

echo "<h3>Processed Subject:</h3>\n";
echo "<p>" . htmlspecialchars($processedSubject1) . "</p>\n";

echo "<h3>Processed Content (first 500 chars):</h3>\n";
echo "<div style='border: 1px solid #ccc; padding: 10px; background: #f9f9f9; max-height: 200px; overflow-y: auto;'>\n";
echo htmlspecialchars(substr($processedContent1, 0, 500)) . "...\n";
echo "</div>\n";

// Check for image HTML
if (strpos($processedContent1, '<img src=') !== false) {
    echo "<p style='color: green; font-weight: bold;'>✅ Image HTML found in content!</p>\n";
} else {
    echo "<p style='color: red; font-weight: bold;'>❌ No image HTML found in content!</p>\n";
}

// Test 2: Using BirthdayReminder class (like admin/send_birthday_emails.php)
echo "<h2>Test 2: Using BirthdayReminder class (admin/send_birthday_emails.php method)</h2>\n";

try {
    require_once 'classes/BirthdayReminder.php';
    $birthdayReminder = new BirthdayReminder($pdo);
    
    // Use the prepareMemberDataWithImage method (if accessible)
    $reflection = new ReflectionClass($birthdayReminder);
    if ($reflection->hasMethod('prepareMemberDataWithImage')) {
        $method = $reflection->getMethod('prepareMemberDataWithImage');
        $method->setAccessible(true);
        $preparedMemberData = $method->invoke($birthdayReminder, $testMember);
        
        $processedSubject2 = replaceTemplatePlaceholders($template['subject'], $preparedMemberData);
        $processedContent2 = replaceTemplatePlaceholders($template['content'], $preparedMemberData);
        
        echo "<h3>Processed Subject:</h3>\n";
        echo "<p>" . htmlspecialchars($processedSubject2) . "</p>\n";
        
        echo "<h3>Processed Content (first 500 chars):</h3>\n";
        echo "<div style='border: 1px solid #ccc; padding: 10px; background: #f9f9f9; max-height: 200px; overflow-y: auto;'>\n";
        echo htmlspecialchars(substr($processedContent2, 0, 500)) . "...\n";
        echo "</div>\n";
        
        // Check for image HTML
        if (strpos($processedContent2, '<img src=') !== false) {
            echo "<p style='color: green; font-weight: bold;'>✅ Image HTML found in content!</p>\n";
        } else {
            echo "<p style='color: red; font-weight: bold;'>❌ No image HTML found in content!</p>\n";
        }
        
        // Compare the two methods
        echo "<h2>Comparison Results:</h2>\n";
        if ($processedContent1 === $processedContent2) {
            echo "<p style='color: green; font-weight: bold;'>✅ Both methods produce identical content!</p>\n";
        } else {
            echo "<p style='color: orange; font-weight: bold;'>⚠️ Methods produce different content (this may be expected due to different processing)</p>\n";
        }
        
    } else {
        echo "<p style='color: orange;'>prepareMemberDataWithImage method not accessible, using direct class test instead.</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error testing BirthdayReminder class: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<h2>Summary</h2>\n";
echo "<p>This test verifies that both email sending methods now properly handle profile pictures:</p>\n";
echo "<ul>\n";
echo "<li><strong>admin/send_birthday_message.php</strong> - Uses replaceTemplatePlaceholders directly</li>\n";
echo "<li><strong>admin/send_birthday_emails.php</strong> - Uses BirthdayReminder class</li>\n";
echo "</ul>\n";
echo "<p>Both should now generate proper HTML img tags for the {member_image} placeholder.</p>\n";

// Test the actual image URL generation
echo "<h2>Image URL Test</h2>\n";
$imageUrl = get_base_url() . '/' . ltrim($testMember['image_path'], '/');
echo "<p><strong>Generated Image URL:</strong> <a href='" . htmlspecialchars($imageUrl) . "' target='_blank'>" . htmlspecialchars($imageUrl) . "</a></p>\n";
echo "<p>Click the link above to verify the image is accessible.</p>\n";

?>
