<?php
// Test script to verify that profile pictures are now working correctly in emails
require_once 'config.php';

echo "<h1>Testing Profile Picture Fix</h1>\n";

// Test member data with a profile picture
$testMember = [
    'id' => 51,
    'full_name' => '<PERSON><PERSON>',
    'first_name' => '<PERSON><PERSON>',
    'last_name' => '<PERSON>int<PERSON>',
    'email' => '<EMAIL>',
    'phone_number' => '(*************',
    'birth_date' => '1990-07-14',
    'image_path' => 'uploads/profiles/687501115a700.jpg'
];

echo "<h2>Test Member Data:</h2>\n";
echo "<pre>" . print_r($testMember, true) . "</pre>\n";

// Test template content with member image placeholder
$testTemplate = "
<h1>Happy Birthday, {first_name}!</h1>
<p>Dear {full_name},</p>
<p>We hope you have a wonderful birthday!</p>
<div style='text-align: center;'>
    {member_image}
</div>
<p>Best wishes from our organization!</p>
";

echo "<h2>Template Content (before processing):</h2>\n";
echo "<pre>" . htmlspecialchars($testTemplate) . "</pre>\n";

// Process the template using the fixed function
$processedContent = replaceTemplatePlaceholders($testTemplate, $testMember, false);

echo "<h2>Processed Content (after placeholder replacement):</h2>\n";
echo "<div style='border: 1px solid #ccc; padding: 10px; background: #f9f9f9;'>\n";
echo $processedContent;
echo "</div>\n";

echo "<h2>Raw HTML (for inspection):</h2>\n";
echo "<pre>" . htmlspecialchars($processedContent) . "</pre>\n";

// Test if the image HTML is properly generated
if (strpos($processedContent, '<img src=') !== false) {
    echo "<div style='color: green; font-weight: bold; padding: 10px; background: #d4edda; border: 1px solid #c3e6cb;'>\n";
    echo "✅ SUCCESS: Image HTML tag found in processed content!\n";
    echo "</div>\n";
} else {
    echo "<div style='color: red; font-weight: bold; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb;'>\n";
    echo "❌ FAILED: No image HTML tag found in processed content!\n";
    echo "</div>\n";
}

// Test with member without image
echo "<h2>Testing with member without profile picture:</h2>\n";
$testMemberNoImage = [
    'id' => 999,
    'full_name' => 'Test User',
    'first_name' => 'Test',
    'last_name' => 'User',
    'email' => '<EMAIL>',
    'image_path' => '' // No image
];

$processedContentNoImage = replaceTemplatePlaceholders($testTemplate, $testMemberNoImage, false);

echo "<div style='border: 1px solid #ccc; padding: 10px; background: #f9f9f9;'>\n";
echo $processedContentNoImage;
echo "</div>\n";

if (strpos($processedContentNoImage, 'default-avatar.png') !== false) {
    echo "<div style='color: green; font-weight: bold; padding: 10px; background: #d4edda; border: 1px solid #c3e6cb;'>\n";
    echo "✅ SUCCESS: Default avatar used for member without profile picture!\n";
    echo "</div>\n";
} else {
    echo "<div style='color: red; font-weight: bold; padding: 10px; background: #f8d7da; border: 1px solid #f5c6cb;'>\n";
    echo "❌ FAILED: Default avatar not found for member without profile picture!\n";
    echo "</div>\n";
}

echo "<h2>Comparison Test:</h2>\n";
echo "<p>This test compares the old behavior (URL only) vs new behavior (HTML tag):</p>\n";

// Simulate old behavior (URL only)
$memberImageUrl = get_base_url() . '/' . ltrim($testMember['image_path'], '/');
$oldBehavior = str_replace('{member_image}', $memberImageUrl, $testTemplate);

echo "<h3>Old Behavior (URL only):</h3>\n";
echo "<pre>" . htmlspecialchars($oldBehavior) . "</pre>\n";

echo "<h3>New Behavior (HTML tag):</h3>\n";
echo "<pre>" . htmlspecialchars($processedContent) . "</pre>\n";

echo "<h2>Test Complete</h2>\n";
echo "<p>If the fix is working correctly, you should see:</p>\n";
echo "<ul>\n";
echo "<li>✅ Image HTML tags in the processed content</li>\n";
echo "<li>✅ Default avatar for members without pictures</li>\n";
echo "<li>✅ Proper styling applied to the image tags</li>\n";
echo "</ul>\n";
?>
